"""
Testes para a classe EmissaoMarketplace

Este arquivo contém testes abrangentes para todos os métodos da classe EmissaoMarketplace,
incluindo:
- Inicialização (__init__)
- Validação (validate)
- Bloqueio de assentos (set_blocked_seat, ensure_blocked_seat)
- Emissão (emit)
- Construção de formulários (build_request_form)
- Métodos estáticos (buyer_cpf, buyer_phone)
- Testes de integração

Os testes cobrem cenários de sucesso e falha, garantindo que a classe funcione
corretamente em diferentes situações.
"""

from datetime import date, timedelta
from decimal import Decimal as D
from unittest.mock import Mock, patch

import pytest
from django.contrib.auth.models import User
from model_bakery import baker

from commons.dateutils import now
from core.enums import CategoriaEspecial
from core.forms.rodoviaria_forms import BuseiroForm, ComprarForm, DadosBeneficioForm
from core.models_commons import Profile
from core.models_grupo import Grupo, TrechoClasse
from core.models_travel import Buseiro, Passageiro, Reserva, Travel
from core.service.reserva.rodoviaria_reserva_svc import EmissaoMarketplace, EmissaoNotAvailable, TicketAlreadyIssued
from core.service.selecao_assento import SeatAlreadyTaken
from core.service.selecao_assento.marketplace import MarketplaceSeatsController
from core.service.selecao_assento.models import Assento, BlockedSeat
from integrations.rodoviaria_client.exceptions import PoltronaExpiradaException


@pytest.fixture
def mock_marketplace_seats_controller():
    """Mock do MarketplaceSeatsController"""
    controller = Mock(spec=MarketplaceSeatsController)
    return controller


@pytest.fixture
def mock_blocked_seat():
    blocked_seat = BlockedSeat(
        poltrona=Assento(numero=15, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() + timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    return blocked_seat


@pytest.fixture
def marketplace_travel(marketplace_company):
    """Travel configurado para marketplace"""
    trecho_classe = baker.make(
        TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE, grupo__company=marketplace_company
    )

    reserva = baker.make(Reserva)

    travel = baker.make(Travel, trecho_classe=trecho_classe, reserva=reserva, max_split_value=D("50.00"))

    return travel


@pytest.fixture
def marketplace_passageiro(marketplace_travel):
    """Passageiro configurado para marketplace"""
    buseiro = baker.make(
        Buseiro,
        name="João Silva",
        cpf="*********01",
        rg_number="*********",
        phone="11999999999",
        birthday=date(1990, 1, 1),
    )

    passageiro = baker.make(Passageiro, travel=marketplace_travel, buseiro=buseiro, poltrona=15, extra={})

    return passageiro


@pytest.fixture
def dados_beneficio():
    """Dados de benefício para testes"""
    return DadosBeneficioForm(
        data_expedicao=date(2020, 1, 1),
        data_expiracao=date(2025, 1, 1),
        numero_beneficio="123456",
        renda=1000,
        tipo_passe_livre="estudante",
        auxilio_embarque=False,
    )


def test_init_com_parametros_completos(marketplace_passageiro, mock_marketplace_seats_controller, dados_beneficio):
    """Testa inicialização com todos os parâmetros"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
    ):
        emissao = EmissaoMarketplace(
            passageiro=marketplace_passageiro,
            seats_controller=mock_marketplace_seats_controller,
            categoria_especial=CategoriaEspecial.JOVEM_50,
            dados_beneficio=dados_beneficio,
        )

        assert emissao.passageiro == marketplace_passageiro
        assert emissao.seats_controller == mock_marketplace_seats_controller
        assert emissao.categoria_especial == CategoriaEspecial.JOVEM_50
        assert emissao.dados_beneficio == dados_beneficio


def test_init_sem_seats_controller(marketplace_passageiro):
    """Testa inicialização sem seats_controller (deve criar um automaticamente)"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
        patch(
            "core.service.reserva.rodoviaria_reserva_svc.marketplace_selecao_assento.MarketplaceSeatsController"
        ) as mock_controller_class,
    ):
        mock_controller = Mock()
        mock_controller_class.return_value = mock_controller

        emissao = EmissaoMarketplace(passageiro=marketplace_passageiro)

        assert emissao.passageiro == marketplace_passageiro
        assert emissao.seats_controller == mock_controller
        mock_controller_class.assert_called_once_with(marketplace_passageiro.travel.trecho_classe)


def test_init_sem_categoria_especial_com_reserva(marketplace_passageiro):
    """Testa inicialização sem categoria especial quando há reserva"""
    marketplace_passageiro.travel.reserva.categoria_especial_info = Mock(
        return_value=(CategoriaEspecial.JOVEM_50, None)
    )

    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
        patch("core.service.reserva.rodoviaria_reserva_svc.marketplace_selecao_assento.MarketplaceSeatsController"),
    ):
        emissao = EmissaoMarketplace(passageiro=marketplace_passageiro)

        assert emissao.categoria_especial == CategoriaEspecial.JOVEM_50
        assert emissao.dados_beneficio is None


def test_init_sem_categoria_especial_sem_reserva(marketplace_passageiro):
    """Testa inicialização sem categoria especial quando não há reserva"""
    marketplace_passageiro.travel.reserva = None

    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
        patch("core.service.reserva.rodoviaria_reserva_svc.marketplace_selecao_assento.MarketplaceSeatsController"),
    ):
        emissao = EmissaoMarketplace(passageiro=marketplace_passageiro)

        assert emissao.categoria_especial == CategoriaEspecial.NORMAL
        assert emissao.dados_beneficio is None


def test_validate_modelo_venda_nao_marketplace(marketplace_passageiro):
    """Testa validação quando modelo de venda não é marketplace"""
    marketplace_passageiro.travel.trecho_classe.grupo.modelo_venda = Grupo.ModeloVenda.HIBRIDO

    with patch("core.service.reserva.rodoviaria_reserva_svc.marketplace_selecao_assento.MarketplaceSeatsController"):
        with pytest.raises(EmissaoNotAvailable, match="Emissão de passageiros é permitida apenas para Marketplace"):
            EmissaoMarketplace(passageiro=marketplace_passageiro)


def test_validate_viagem_nao_integrada(marketplace_passageiro):
    """Testa validação quando viagem não é integrada"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=False),
        patch("core.service.reserva.rodoviaria_reserva_svc.marketplace_selecao_assento.MarketplaceSeatsController"),
    ):
        with pytest.raises(
            EmissaoNotAvailable, match="Emissão de passageiros não é permitida para viagens não integradas"
        ):
            EmissaoMarketplace(passageiro=marketplace_passageiro)


def test_validate_passagem_ja_emitida(marketplace_passageiro):
    """Testa validação quando passagem já foi emitida"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch(
            "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria",
            return_value=[{"id": 1}],
        ),
        patch("core.service.reserva.rodoviaria_reserva_svc.marketplace_selecao_assento.MarketplaceSeatsController"),
    ):
        with pytest.raises(TicketAlreadyIssued):
            EmissaoMarketplace(passageiro=marketplace_passageiro)


def test_validate_sucesso(marketplace_passageiro):
    """Testa validação bem-sucedida"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
        patch("core.service.reserva.rodoviaria_reserva_svc.marketplace_selecao_assento.MarketplaceSeatsController"),
    ):
        # Não deve lançar exceção
        emissao = EmissaoMarketplace(passageiro=marketplace_passageiro)
        assert emissao.passageiro == marketplace_passageiro


def test_set_blocked_seat(marketplace_passageiro, mock_marketplace_seats_controller, mock_blocked_seat):
    """Testa bloqueio de assento com sucesso"""
    mock_marketplace_seats_controller.bloquear_poltrona.return_value = mock_blocked_seat

    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
    ):
        emissao = EmissaoMarketplace(
            passageiro=marketplace_passageiro, seats_controller=mock_marketplace_seats_controller
        )

        resultado = emissao.set_blocked_seat(15)

        marketplace_passageiro.refresh_from_db()
        assert resultado == mock_blocked_seat
        assert marketplace_passageiro.poltrona == 15
        assert "bloqueio_poltrona" in marketplace_passageiro.extra
        mock_marketplace_seats_controller.bloquear_poltrona.assert_called_once()


def test_build_request_form(mocker, marketplace_passageiro, mock_blocked_seat):
    marketplace_passageiro.extra["bloqueio_poltrona"] = mock_blocked_seat.model_dump()
    mocker.patch.object(EmissaoMarketplace, "buyer_phone", return_value="*********0")
    mocker.patch.object(EmissaoMarketplace, "buyer_cpf", return_value="1991010921")

    dto = EmissaoMarketplace.build_request_form(
        marketplace_passageiro,
        categoria_especial=CategoriaEspecial.CRIANCA,
        dados_beneficio=DadosBeneficioForm(numero_beneficio="numero"),  # type: ignore
    )

    assert dto == ComprarForm(
        trechoclasse_id=marketplace_passageiro.travel.trecho_classe_id,
        travel_id=marketplace_passageiro.travel.id,
        valor_cheio=marketplace_passageiro.travel.max_split_value,
        poltronas=[marketplace_passageiro.poltrona],  # type: ignore
        extra_poltronas=mock_blocked_seat.external_payload,
        categoria_especial=CategoriaEspecial.CRIANCA,
        buseiros=[
            BuseiroForm(
                id=marketplace_passageiro.buseiro.id,
                name=marketplace_passageiro.buseiro.name,
                cpf=marketplace_passageiro.buseiro.cpf,
                buyer_cpf="1991010921",
                rg_number=marketplace_passageiro.buseiro.rg_number,
                rg_orgao=marketplace_passageiro.buseiro.rg_orgao,
                tipo_documento=marketplace_passageiro.buseiro.tipo_documento,
                phone="*********0",
                birthday=marketplace_passageiro.buseiro.birthday,
                dados_beneficio=DadosBeneficioForm(
                    renda=None,
                    data_expiracao=None,
                    data_expedicao=None,
                    numero_beneficio="numero",
                    tipo_passe_livre=None,
                    auxilio_embarque=None,
                ),
            )
        ],
    )


def test_ensure_blocked_seat_poltrona_ja_bloqueada(
    mocker, marketplace_passageiro, mock_blocked_seat, mock_marketplace_seats_controller
):
    marketplace_passageiro.extra["bloqueio_poltrona"] = mock_blocked_seat.model_dump()
    mock_marketplace_seats_controller.bloquear_poltrona.return_value = mock_blocked_seat

    mocker.patch.object(EmissaoMarketplace, "validate")

    EmissaoMarketplace(marketplace_passageiro, mock_marketplace_seats_controller).ensure_blocked_seat()

    mock_marketplace_seats_controller.bloquear_poltrona.assert_not_called()


def test_ensure_blocked_seat_bloqueia_mesma_poltrona_travel_anterior(
    mocker, marketplace_passageiro, mock_blocked_seat, mock_marketplace_seats_controller
):
    mocker.patch.object(EmissaoMarketplace, "validate")
    mock_marketplace_seats_controller.bloquear_poltrona.return_value = mock_blocked_seat
    assert marketplace_passageiro.extra == {}  # simula bloqueio antigo/inexistente

    EmissaoMarketplace(marketplace_passageiro, mock_marketplace_seats_controller).ensure_blocked_seat()

    marketplace_passageiro.refresh_from_db()
    assert marketplace_passageiro.poltrona == 15
    assert marketplace_passageiro.extra["bloqueio_poltrona"]["poltrona"]["numero"] == 15


def test_ensure_blocked_seat_escolhe_nova_caso_poltrona_indisponivel(
    mocker, marketplace_passageiro, mock_blocked_seat, mock_marketplace_seats_controller
):
    mocker.patch.object(EmissaoMarketplace, "validate")
    mock_marketplace_seats_controller.bloquear_poltrona.side_effect = SeatAlreadyTaken()

    mock_blocked_seat.poltrona.numero = 40
    mock_marketplace_seats_controller.escolhe_e_bloqueia_poltronas.return_value = [mock_blocked_seat]

    EmissaoMarketplace(marketplace_passageiro, mock_marketplace_seats_controller).ensure_blocked_seat()

    marketplace_passageiro.refresh_from_db()
    assert marketplace_passageiro.poltrona == 40
    assert marketplace_passageiro.extra["seat_already_taken"] is True


def test_buyer_phone_from_travel_user():
    travel = baker.make(Travel, user=baker.make(User, profile=baker.make(Profile, cell_phone="*********0")))
    assert EmissaoMarketplace.buyer_phone(travel) == "*********0"


def test_buyer_phone_from_buseiro():
    travel = baker.make(Travel)
    user = baker.make(User)
    baker.make(Profile, user=user, cell_phone="*********0")

    buseiro = baker.make(Buseiro, user=user)
    baker.make(Passageiro, travel=travel, buseiro=buseiro)

    assert EmissaoMarketplace.buyer_phone(travel) == "*********0"


def test_buyer_cpf_from_travel_user():
    travel = baker.make(Travel, user=baker.make(User, profile=baker.make(Profile, cpf="*********01")))
    assert EmissaoMarketplace.buyer_cpf(travel) == "*********01"


def test_buyer_cpf_from_buseiro():
    travel = baker.make(Travel)
    buseiro = baker.make(Buseiro, cpf="*********01")
    baker.make(Passageiro, travel=travel, buseiro=buseiro)

    assert EmissaoMarketplace.buyer_cpf(travel) == "*********01"


def test_buyer_cpf_returns_none_when_no_cpf_available():
    travel = baker.make(Travel)
    buseiro = baker.make(Buseiro, cpf=None)
    baker.make(Passageiro, travel=travel, buseiro=buseiro)

    assert EmissaoMarketplace.buyer_cpf(travel) is None


def test_buyer_cpf_prioritizes_travel_user_over_buseiro():
    travel = baker.make(Travel, user=baker.make(User, profile=baker.make(Profile, cpf="*********01")))
    buseiro = baker.make(Buseiro, cpf="98765432109")
    baker.make(Passageiro, travel=travel, buseiro=buseiro)

    assert EmissaoMarketplace.buyer_cpf(travel) == "*********01"


def test_emit(mocker, marketplace_passageiro, mock_marketplace_seats_controller, mock_blocked_seat):
    mocker.patch.object(EmissaoMarketplace, "validate")
    mock_marketplace_seats_controller.bloquear_poltrona.return_value = mock_blocked_seat

    mock_emitir_passagem_rodoviaria = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica", return_value="retorno"
    )
    EmissaoMarketplace(marketplace_passageiro, mock_marketplace_seats_controller).emit()

    marketplace_passageiro.refresh_from_db()
    assert marketplace_passageiro.typed_extra["emissao"]["sucesso"] is True
    assert mock_emitir_passagem_rodoviaria.call_count == 1
    assert mock_emitir_passagem_rodoviaria.mock_calls[0][1][0].extra_poltronas == mock_blocked_seat.external_payload


def test_emit_erro(mocker, marketplace_passageiro, mock_marketplace_seats_controller, mock_blocked_seat):
    mocker.patch.object(EmissaoMarketplace, "validate")
    mock_marketplace_seats_controller.bloquear_poltrona.return_value = mock_blocked_seat

    mock_emitir_passagem_rodoviaria = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica", side_effect=Exception("Error")
    )
    with pytest.raises(Exception, match="Error"):
        EmissaoMarketplace(marketplace_passageiro, mock_marketplace_seats_controller).emit()

    marketplace_passageiro.refresh_from_db()
    assert marketplace_passageiro.typed_extra["emissao"]["sucesso"] is False
    assert mock_emitir_passagem_rodoviaria.call_count == 1
    assert mock_emitir_passagem_rodoviaria.mock_calls[0][1][0].extra_poltronas == mock_blocked_seat.external_payload


def test_emit_erro_poltrona_expirada(
    mocker, marketplace_passageiro, mock_marketplace_seats_controller, mock_blocked_seat
):
    mocker.patch.object(EmissaoMarketplace, "validate")
    mock_blocked_seat2 = mock_blocked_seat
    mock_blocked_seat2.external_payload = {"External2": "Payload 2"}
    mock_marketplace_seats_controller.bloquear_poltrona.side_effect = [mock_blocked_seat, mock_blocked_seat2]

    mock_emitir_passagem_rodoviaria = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica",
        side_effect=[PoltronaExpiradaException("Error"), "retorno"],
    )
    EmissaoMarketplace(marketplace_passageiro, mock_marketplace_seats_controller).emit()

    marketplace_passageiro.refresh_from_db()
    assert marketplace_passageiro.typed_extra["emissao"]["sucesso"] is True
    assert mock_emitir_passagem_rodoviaria.call_count == 2
    assert mock_emitir_passagem_rodoviaria.mock_calls[0][1][0].extra_poltronas == mock_blocked_seat.external_payload
    assert mock_emitir_passagem_rodoviaria.mock_calls[1][1][0].extra_poltronas == mock_blocked_seat2.external_payload
